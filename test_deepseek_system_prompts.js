// 测试脚本来验证 DeepSeek 是否正确发送 system prompts

async function runTest() {
  console.log('🚀 Starting DeepSeek system prompts test...');

  try {
    const { Config } = await import('./packages/core/dist/src/config/config.js');
    const { createContentGenerator } = await import('./packages/core/dist/src/core/contentGenerator.js');
    const { getCoreSystemPrompt } = await import('./packages/core/dist/src/core/prompts.js');
    const axios = (await import('axios')).default;

    // Store the original axios.post method
    const originalPost = axios.post;

// Mock axios.post to capture the request
let capturedRequest = null;
axios.post = async (url, data, config) => {
  console.log('🔍 Intercepted API call to:', url);
  console.log('📝 Request data:', JSON.stringify(data, null, 2));
  console.log('⚙️ Request config:', JSON.stringify(config, null, 2));
  
  capturedRequest = { url, data, config };
  
  // Return a mock response
  return {
    data: {
      choices: [{
        message: {
          content: 'Mock response from DeepSeek API'
        }
      }],
      usage: {
        prompt_tokens: 100,
        completion_tokens: 50,
        total_tokens: 150
      }
    }
  };
};

async function testDeepSeekSystemPrompts() {
  console.log('🧪 Testing DeepSeek system prompts...\n');

  try {
    // Create DeepSeek config
    const config = new Config({
      provider: 'deepseek',
      targetDir: process.cwd(),
      telemetry: {
        enabled: false
      },
      providers: {
        deepseek: {
          apiKey: 'test-api-key-for-mocking'
        }
      }
    });

    // Get the system prompt
    const systemPrompt = getCoreSystemPrompt();
    console.log('📋 System prompt length:', systemPrompt.length);
    console.log('📋 System prompt preview:', systemPrompt.substring(0, 200) + '...\n');

    // Create content generator
    const contentGenerator = await createContentGenerator(config);

    // Test generateContent with system prompt
    console.log('🚀 Testing generateContent with system prompt...');
    
    const response = await contentGenerator.generateContent({
      model: 'deepseek-chat',
      contents: [
        {
          role: 'user',
          parts: [{ text: 'Hello, can you help me with a coding task?' }]
        }
      ],
      config: {
        systemInstruction: systemPrompt,
        temperature: 0.7
      }
    });

    console.log('✅ Response received:', response.candidates?.[0]?.content?.parts?.[0]?.text);

    // Analyze the captured request
    if (capturedRequest) {
      console.log('\n🔍 Analysis of captured request:');
      console.log('URL:', capturedRequest.url);
      console.log('Model:', capturedRequest.data.model);
      console.log('Messages count:', capturedRequest.data.messages?.length || 0);
      
      if (capturedRequest.data.messages) {
        capturedRequest.data.messages.forEach((msg, index) => {
          console.log(`Message ${index + 1}:`);
          console.log(`  Role: ${msg.role}`);
          console.log(`  Content length: ${msg.content?.length || 0}`);
          if (msg.role === 'system') {
            console.log(`  Content preview: ${msg.content?.substring(0, 100)}...`);
          } else {
            console.log(`  Content: ${msg.content}`);
          }
        });
      }

      // Verify system prompt was sent
      const systemMessage = capturedRequest.data.messages?.find(msg => msg.role === 'system');
      if (systemMessage) {
        console.log('\n✅ SUCCESS: System prompt was correctly sent to DeepSeek API!');
        console.log('✅ System message content length:', systemMessage.content.length);
        
        // Check if it contains expected system prompt content
        if (systemMessage.content.includes('interactive CLI agent')) {
          console.log('✅ System prompt contains expected content');
        } else {
          console.log('❌ System prompt does not contain expected content');
        }
      } else {
        console.log('\n❌ FAILURE: No system prompt found in the request!');
      }

      // Check other parameters
      console.log('\n📊 Other request parameters:');
      console.log('Temperature:', capturedRequest.data.temperature);
      console.log('Top P:', capturedRequest.data.top_p);
      console.log('Max tokens:', capturedRequest.data.max_tokens);
    } else {
      console.log('\n❌ No request was captured!');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    // Restore original axios.post
    axios.post = originalPost;
  }
}

    // Run the test
    await testDeepSeekSystemPrompts();
    console.log('\n🏁 Test completed');
  } catch (error) {
    console.error('💥 Test crashed:', error);
  }
}

runTest();
